# 模态框宽度和下拉框样式统一

## 修改概述

统一所有弹性伸缩相关模态框的宽度，并保持资源池类型和集群类型下拉框的显示样式一致。

## 修改内容

### 1. 模态框宽度统一

#### 修改前的宽度设置：
- **创建策略模态框**: 800px
- **编辑策略模态框**: 800px  
- **创建订单模态框**: 900px ❌

#### 修改后的宽度设置：
- **创建策略模态框**: 800px ✅
- **编辑策略模态框**: 800px ✅
- **创建订单模态框**: 800px ✅

**文件**: `web/navy-fe/src/components/ElasticScaling/CreateOrderModal.tsx`
**行号**: 409
**修改**: `width={900}` → `width={800}`

### 2. 下拉框样式统一

#### 2.1 集群选择器样式统一

**文件**: `web/navy-fe/src/components/ElasticScaling/CreateOrderModal.tsx`
**位置**: 集群选择Form.Item

**修改前**:
```tsx
<Select 
  placeholder="请选择集群"
  showSearch
  optionFilterProp="children"
  filterOption={(input, option) => 
    (option?.children?.toString().toLowerCase().indexOf(input.toLowerCase()) ?? -1) >= 0
  }
>
  {clusters.map(cluster => (
    <Option key={cluster.id} value={cluster.id}>{cluster.name}</Option>
  ))}
</Select>
```

**修改后**:
```tsx
<Select 
  placeholder="请选择集群"
  showSearch
  optionFilterProp="children"
  filterOption={(input, option) => 
    (option?.children?.toString().toLowerCase().indexOf(input.toLowerCase()) ?? -1) >= 0
  }
  showArrow
  style={{ width: '100%' }}
>
  {clusters.map(cluster => (
    <Option key={cluster.id} value={cluster.id}>
      <ClusterOutlined style={{ marginRight: 4, color: '#52c41a' }} />
      {cluster.name}
    </Option>
  ))}
</Select>
```

#### 2.2 资源池类型选择器样式统一

**文件**: `web/navy-fe/src/components/ElasticScaling/CreateOrderModal.tsx`
**位置**: 资源池类型选择Form.Item

**修改前**:
```tsx
<Select 
  placeholder="请选择资源池类型"
  showSearch
  optionFilterProp="children"
  filterOption={(input, option) => 
    (option?.children?.toString().toLowerCase().indexOf(input.toLowerCase()) ?? -1) >= 0
  }
>
  {resourcePools.map(pool => (
    <Option key={pool.type} value={pool.type}>{pool.type}</Option>
  ))}
</Select>
```

**修改后**:
```tsx
<Select 
  placeholder="请选择资源池类型"
  showSearch
  optionFilterProp="children"
  filterOption={(input, option) => 
    (option?.children?.toString().toLowerCase().indexOf(input.toLowerCase()) ?? -1) >= 0
  }
  showArrow
  style={{ width: '100%' }}
>
  {resourcePools.map(pool => (
    <Option key={pool.type} value={pool.type}>
      <DatabaseOutlined style={{ marginRight: 4, color: '#1890ff' }} />
      {pool.type}
    </Option>
  ))}
</Select>
```

### 3. 样式统一特点

#### 3.1 一致的视觉元素
- **图标**: 所有选项都带有相应的图标
  - 集群选择器: `ClusterOutlined` (绿色 #52c41a)
  - 资源池类型: `DatabaseOutlined` (蓝色 #1890ff)
- **间距**: 图标与文本间距统一为 4px
- **箭头**: 所有下拉框都显示箭头 (`showArrow`)
- **宽度**: 明确设置 `width: '100%'`

#### 3.2 保持的功能特性
- **搜索功能**: 保持 `showSearch` 和 `optionFilterProp`
- **过滤逻辑**: 保持原有的过滤函数
- **单选逻辑**: 创建订单模态框保持单选，不改变为多选
- **多选逻辑**: 策略模态框保持多选和自定义标签渲染

### 4. 不同模态框的选择器对比

#### 4.1 创建/编辑策略模态框
- **资源池类型**: 多选 + 自定义标签渲染
- **集群选择**: 多选 + 自定义标签渲染
- **图标**: 带图标的选项

#### 4.2 创建订单模态框
- **资源池类型**: 单选 + 图标选项
- **集群选择**: 单选 + 图标选项
- **图标**: 与策略模态框相同的图标和颜色

## 测试要点

### 1. 模态框宽度测试
- [ ] 创建订单模态框宽度为800px
- [ ] 三个模态框宽度保持一致
- [ ] 模态框内容布局正常，无溢出

### 2. 下拉框样式测试
- [ ] 集群选择器显示绿色集群图标
- [ ] 资源池类型选择器显示蓝色数据库图标
- [ ] 图标与文本间距合适
- [ ] 下拉箭头正常显示

### 3. 功能测试
- [ ] 创建订单模态框保持单选逻辑
- [ ] 策略模态框保持多选逻辑
- [ ] 搜索功能正常工作
- [ ] 选择功能正常工作

### 4. 视觉一致性测试
- [ ] 所有模态框的下拉框样式一致
- [ ] 图标颜色和大小一致
- [ ] 整体视觉效果协调

## 优势

1. **视觉一致性**: 所有模态框宽度统一，提供一致的用户体验
2. **样式统一**: 下拉框选项都带有相应图标，提升可识别性
3. **功能保持**: 不改变原有的单选/多选逻辑，避免功能破坏
4. **用户体验**: 统一的视觉元素降低用户学习成本

## 注意事项

1. **功能完整性**: 修改仅涉及样式，不影响原有业务逻辑
2. **兼容性**: 保持与现有API和数据结构的兼容
3. **响应式**: 确保在不同屏幕尺寸下正常显示
4. **性能**: 样式修改不影响组件性能
