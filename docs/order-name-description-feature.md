# 订单名称和描述功能实现

## 功能概述

为弹性伸缩订单添加名称和描述字段，提升订单管理的可读性和可追溯性。

## 实现内容

### 1. 后端修改

#### 1.1 数据模型更新
- **文件**: `models/portal/elastic_scaling_order.go`
- **修改**: 添加 `Name` 和 `Description` 字段
```go
Name        string `gorm:"column:name;type:varchar(255)" json:"name"`        // 订单名称
Description string `gorm:"column:description;type:text" json:"description"` // 订单描述
```

#### 1.2 DTO更新
- **文件**: `server/portal/internal/service/elastic_scaling_dto.go`
- **修改**: 
  - `OrderDTO` 添加 `Name` 和 `Description` 字段
  - `OrderListItemDTO` 添加 `Name` 和 `Description` 字段

#### 1.3 服务层更新
- **文件**: `server/portal/internal/service/elastic_scaling_service.go`
- **修改**:
  - `CreateOrder` 方法支持保存订单名称和描述
  - `GetOrder` 方法返回订单名称和描述
  - `ListOrders` 方法返回订单名称和描述

### 2. 前端修改

#### 2.1 类型定义更新
- **文件**: `web/navy-fe/src/types/elastic-scaling.ts`
- **修改**: `OrderListItem` 接口添加 `name` 和 `description` 字段

#### 2.2 显示逻辑更新
- **文件**: `web/navy-fe/src/components/ElasticScaling/Dashboard.tsx`
- **修改**:
  - 订单卡片标题显示订单名称（优先级：策略名称 > 订单名称 > 订单号）
  - 订单详情页面显示订单名称和描述
  - Drawer标题显示订单名称

### 3. 数据库迁移

#### 3.1 迁移脚本
- **文件**: `migrations/add_order_name_description.sql`
- **内容**:
  - 添加 `name` 和 `description` 字段
  - 为现有数据设置默认值
  - 添加索引优化查询性能

## 显示逻辑

### 订单卡片标题
```
优先级：策略名称 > 订单名称 > 订单号
- 策略触发订单：显示策略名称
- 手动创建订单：显示订单名称（如果有），否则显示订单号
```

### 订单详情
```
- 订单名称：仅在有值时显示
- 订单描述：仅在有值时显示
- 其他字段：正常显示
```

## 测试要点

### 1. 后端测试
- [ ] 创建订单时能正确保存名称和描述
- [ ] 获取订单列表时能正确返回名称和描述
- [ ] 获取订单详情时能正确返回名称和描述
- [ ] 策略触发的订单能正确关联策略信息

### 2. 前端测试
- [ ] 订单卡片标题正确显示（策略名称/订单名称/订单号）
- [ ] 订单详情页面正确显示名称和描述
- [ ] 空值字段不显示（优雅降级）
- [ ] Drawer标题正确显示

### 3. 数据库测试
- [ ] 迁移脚本能正确执行
- [ ] 现有数据能正确设置默认值
- [ ] 索引能正确创建
- [ ] 查询性能符合预期

## API 示例

### 创建订单
```json
POST /fe-v1/elastic-scaling/orders
{
  "name": "紧急扩容订单",
  "description": "由于流量激增需要紧急扩容",
  "clusterId": 1,
  "actionType": "pool_entry",
  "devices": [1, 2, 3],
  "deviceCount": 3
}
```

### 订单列表响应
```json
{
  "data": {
    "list": [
      {
        "id": 1,
        "orderNumber": "ESO20241201001",
        "name": "紧急扩容订单",
        "description": "由于流量激增需要紧急扩容",
        "clusterId": 1,
        "clusterName": "生产集群",
        "actionType": "pool_entry",
        "status": "pending",
        "deviceCount": 3,
        "createdBy": "admin",
        "createdAt": "2024-12-01T10:00:00Z"
      }
    ]
  }
}
```

## 兼容性

### 向后兼容
- 现有API保持兼容
- 新字段为可选字段
- 现有数据通过迁移脚本设置默认值

### 前端兼容
- 优雅降级：如果没有名称则显示订单号
- 条件渲染：只在有值时显示名称和描述字段

## 部署步骤

1. **数据库迁移**
   ```bash
   mysql -u username -p database_name < migrations/add_order_name_description.sql
   ```

2. **后端部署**
   - 部署更新后的后端代码
   - 验证API功能正常

3. **前端部署**
   - 部署更新后的前端代码
   - 验证显示效果正常

4. **功能验证**
   - 创建新订单测试
   - 查看现有订单显示
   - 验证详情页面功能
