# 策略列表与设备匹配策略列表样式统一

## 修改概述

将监控策略管理页面的策略列表操作栏样式调整为与设备匹配策略列表保持一致，提供统一的用户体验。

## 对比分析

### 修改前的差异

#### 策略列表（修改前）
```tsx
<Space size="small">
  <Tooltip title="编辑">
    <Button type="text" icon={<EditOutlined />} onClick={() => editStrategy(record.id)} />
  </Tooltip>
  <Tooltip title={record.status === 'enabled' ? '禁用' : '启用'}>
    <Button
      type="text"
      icon={record.status === 'enabled' ? <CloseCircleOutlined /> : <CheckCircleOutlined />}
      danger={record.status === 'enabled'}
      onClick={() => toggleStrategyStatus(record.id, record.status)}
    />
  </Tooltip>
  <Tooltip title="删除">
    <Button
      type="text"
      danger
      icon={<DeleteOutlined />}
      onClick={() => deleteStrategy(record.id)}
    />
  </Tooltip>
</Space>
```

#### 设备匹配策略列表（参考标准）
```tsx
<Space size="middle" className="action-buttons">
  <Tooltip title="编辑" placement="top">
    <Button
      type="text"
      icon={<EditOutlined />}
      onClick={() => handleEdit(record.id!)}
      className="edit-button"
    />
  </Tooltip>
  <Tooltip title={record.status === 'enabled' ? '禁用' : '启用'} placement="top">
    <Button
      type="text"
      icon={record.status === 'enabled' ? <CloseCircleOutlined /> : <CheckCircleOutlined />}
      danger={record.status === 'enabled'}
      onClick={() => handleToggleStatus(record.id!, record.status)}
      className={record.status === 'enabled' ? "disable-button" : "enable-button"}
    />
  </Tooltip>
  <Tooltip title="删除" placement="top">
    <Button
      type="text"
      danger
      icon={<DeleteOutlined />}
      onClick={() => handleDelete(record.id!)}
      className="delete-button"
    />
  </Tooltip>
</Space>
```

## 统一后的修改

### 1. 表格列配置更新

#### 修改内容
```tsx
{
  title: '操作',
  key: 'action',
  width: 150,           // 新增：固定宽度
  align: 'center',      // 新增：居中对齐
  render: (text: string, record: Strategy) => (
    <Space size="middle" className="action-buttons">  // 修改：间距和类名
      <Tooltip title="编辑" placement="top">          // 修改：添加placement
        <Button
          type="text"
          icon={<EditOutlined />}
          onClick={() => editStrategy(record.id)}
          className="edit-button"                     // 新增：CSS类名
        />
      </Tooltip>
      <Tooltip title={record.status === 'enabled' ? '禁用' : '启用'} placement="top">
        <Button
          type="text"
          icon={record.status === 'enabled' ? <CloseCircleOutlined /> : <CheckCircleOutlined />}
          danger={record.status === 'enabled'}
          onClick={() => toggleStrategyStatus(record.id, record.status)}
          className={record.status === 'enabled' ? "disable-button" : "enable-button"}  // 新增：动态类名
        />
      </Tooltip>
      <Tooltip title="删除" placement="top">
        <Button
          type="text"
          danger
          icon={<DeleteOutlined />}
          onClick={() => deleteStrategy(record.id)}
          className="delete-button"                   // 新增：CSS类名
        />
      </Tooltip>
    </Space>
  ),
},
```

### 2. CSS样式新增

#### 文件位置
`web/navy-fe/src/components/ElasticScaling/Dashboard.css`

#### 新增样式
```css
/* 操作按钮样式 - 与设备匹配策略保持一致 */
.strategy-table .action-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
}

.strategy-table .action-buttons .ant-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 4px;
  transition: all 0.3s;
}

.strategy-table .action-buttons .edit-button:hover {
  background-color: #e6f7ff;
  border-color: #91d5ff;
  color: #1890ff;
}

.strategy-table .action-buttons .enable-button:hover {
  background-color: #f6ffed;
  border-color: #b7eb8f;
  color: #52c41a;
}

.strategy-table .action-buttons .disable-button:hover {
  background-color: #fff2e8;
  border-color: #ffbb96;
  color: #fa541c;
}

.strategy-table .action-buttons .delete-button:hover {
  background-color: #fff1f0;
  border-color: #ffa39e;
  color: #f5222d;
}
```

## 统一的特点

### 1. 布局一致性
- **列宽度**: 固定150px宽度
- **对齐方式**: 居中对齐
- **按钮间距**: 使用`size="middle"`
- **容器类名**: 统一使用`action-buttons`类

### 2. 交互一致性
- **Tooltip位置**: 统一使用`placement="top"`
- **按钮尺寸**: 32x32px固定尺寸
- **悬停效果**: 统一的颜色变化规则

### 3. 视觉一致性
- **按钮样式**: 统一的圆角和过渡效果
- **颜色语义**: 
  - 编辑按钮: 蓝色系 (#1890ff)
  - 启用按钮: 绿色系 (#52c41a)
  - 禁用按钮: 橙色系 (#fa541c)
  - 删除按钮: 红色系 (#f5222d)

### 4. 功能一致性
- **按钮类型**: 统一使用`type="text"`
- **图标使用**: 保持相同的图标选择
- **状态逻辑**: 相同的启用/禁用状态判断

## 用户体验改进

### 1. 视觉统一性
- 用户在不同页面间切换时获得一致的操作体验
- 减少学习成本，提高操作效率

### 2. 交互反馈
- 统一的悬停效果提供清晰的交互反馈
- 颜色语义化帮助用户快速识别操作类型

### 3. 空间利用
- 固定的列宽度确保表格布局稳定
- 居中对齐提供更好的视觉平衡

## 测试要点

### 1. 样式一致性测试
- [ ] 操作栏宽度为150px
- [ ] 按钮居中对齐
- [ ] 按钮间距适中
- [ ] 悬停效果正常

### 2. 功能完整性测试
- [ ] 编辑按钮功能正常
- [ ] 启用/禁用切换正常
- [ ] 删除功能正常
- [ ] Tooltip显示正确

### 3. 响应式测试
- [ ] 不同屏幕尺寸下显示正常
- [ ] 按钮不会重叠或变形

### 4. 跨页面对比测试
- [ ] 策略列表与设备匹配策略列表操作栏样式完全一致
- [ ] 悬停效果和颜色使用保持统一

## 维护建议

### 1. 样式复用
- 考虑将操作按钮样式提取为公共组件
- 建立统一的操作栏设计规范

### 2. 主题支持
- 确保样式在不同主题下正常显示
- 考虑深色模式的适配

### 3. 可访问性
- 确保按钮有足够的点击区域
- 保持良好的颜色对比度

### 4. 扩展性
- 为未来可能的新操作按钮预留样式规则
- 保持样式命名的一致性和可维护性
