# 策略阈值可视化设计方案

## 设计概述

将策略列表中的CPU/内存阈值和目标值合并到一列中，采用类似期权行权波动图的设计，直观展示"触发阈值→目标值"的变化过程。

## 设计方案

### 1. 整体布局

**新的列结构**：
- **阈值配置**：合并CPU/内存的阈值和目标值
- **时间配置**：保留持续时间和冷却时间
- **其他列**：保持不变

### 2. 阈值配置列设计

#### 2.1 视觉元素
```
CPU  [85%] ↓ [70%]
MEM  [90%] ↓ [75%]
```

#### 2.2 组件结构
- **资源标识**：CPU/MEM 标签（不同颜色区分）
- **触发阈值**：红色背景标签（表示警告状态）
- **波动箭头**：根据动作类型显示方向
  - 入池：↓（绿色，表示降低使用率）
  - 退池：↑（橙色，表示可能增加使用率）
- **目标值**：绿色背景标签（表示期望状态）

#### 2.3 颜色语义
- **CPU标识**：蓝色 (#1890ff)
- **内存标识**：紫色 (#722ed1)
- **触发阈值**：红色背景 (#ff4d4f) - 表示需要干预的状态
- **目标值**：绿色背景 (#52c41a) - 表示期望达到的状态
- **入池箭头**：绿色 (#52c41a) - 表示优化方向
- **退池箭头**：橙色 (#ff7a45) - 表示释放资源

### 3. 实现细节

#### 3.1 布局结构
```tsx
<div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
  {/* CPU配置行 */}
  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
    <span>CPU</span>
    <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
      <div className="threshold-tag">[85%]</div>
      <ArrowDownOutlined />
      <div className="target-tag">[70%]</div>
    </div>
  </div>
  
  {/* 内存配置行 */}
  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
    <span>MEM</span>
    <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
      <div className="threshold-tag">[90%]</div>
      <ArrowDownOutlined />
      <div className="target-tag">[75%]</div>
    </div>
  </div>
</div>
```

#### 3.2 样式规范
- **标签尺寸**：最小宽度35px，确保数字居中对齐
- **字体大小**：11px，保持紧凑
- **间距**：元素间4px间距，行间8px间距
- **圆角**：4px圆角，现代化外观

### 4. 用户体验优势

#### 4.1 直观性
- **一目了然**：用户可以快速理解策略的触发条件和期望结果
- **视觉流程**：从红色（问题）→箭头（动作）→绿色（解决）的视觉流程
- **空间效率**：将4个数值压缩到一列中，节省表格空间

#### 4.2 语义化
- **颜色语义**：红色表示警告，绿色表示目标，符合用户认知
- **箭头方向**：直观表示资源使用率的变化趋势
- **标签形式**：类似股票/期权的价格标签，专业且易懂

#### 4.3 可扩展性
- **条件显示**：只显示已配置的资源类型
- **动态适应**：根据策略类型自动调整箭头方向
- **响应式**：在不同屏幕尺寸下保持可读性

### 5. 实际显示效果

#### 5.1 入池策略示例
```
CPU  [85%] ↓ [70%]  (高使用率时入池降低到70%)
MEM  [90%] ↓ [75%]  (高使用率时入池降低到75%)
```

#### 5.2 退池策略示例
```
CPU  [30%] ↑ [50%]  (低使用率时退池可能上升到50%)
MEM  [25%] ↑ [45%]  (低使用率时退池可能上升到45%)
```

#### 5.3 单一资源配置
```
CPU  [80%] ↓ [65%]  (仅配置CPU阈值)
```

### 6. 技术实现

#### 6.1 条件渲染
- 检查 `cpuThresholdValue` 和 `cpuTargetValue` 是否都存在
- 检查 `memoryThresholdValue` 和 `memoryTargetValue` 是否都存在
- 只渲染已配置的资源类型

#### 6.2 动态箭头
- 根据 `thresholdTriggerAction` 字段决定箭头方向
- `pool_entry`：向下箭头（降低使用率）
- `pool_exit`：向上箭头（可能增加使用率）

#### 6.3 响应式设计
- 列宽度设置为200px，确保内容完整显示
- 使用flex布局，在小屏幕上自动调整

### 7. 对比优势

#### 7.1 修改前
- CPU阈值和内存阈值分别占用两列
- 目标值信息隐藏在"配置"列的文本中
- 用户需要在多列间对比才能理解完整逻辑

#### 7.2 修改后
- 所有阈值信息集中在一列
- 触发条件和目标结果的关系一目了然
- 类似交易图表的专业视觉效果
- 节省表格空间，提高信息密度

### 8. 后续优化建议

1. **交互增强**：可以考虑添加hover效果显示详细信息
2. **动画效果**：可以添加微动画展示数值变化过程
3. **图表集成**：未来可以考虑集成小型图表显示历史趋势
4. **自定义主题**：支持不同的颜色主题适配企业品牌
