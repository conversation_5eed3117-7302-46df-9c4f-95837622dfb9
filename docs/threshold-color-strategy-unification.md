# 策略阈值颜色策略统一

## 修改概述

将策略列表中的阈值配置颜色与订单中CPU/内存分配率的分级颜色策略保持一致，提供统一的视觉体验和语义化的颜色表达。

## 颜色分级策略

### 1. 统一的颜色分级规则

基于百分比值的四级颜色分级：

| 百分比范围 | 颜色代码 | 颜色名称 | 语义含义 |
|-----------|---------|---------|----------|
| ≥ 80% | `#f5222d` | 红色 | 高风险/紧急状态 |
| 70% - 79% | `#faad14` | 黄色 | 中风险/警告状态 |
| 55% - 69% | `#52c41a` | 绿色 | 正常/理想状态 |
| < 55% | `#1890ff` | 蓝色 | 低使用率/保守状态 |

### 2. 应用场景

#### 2.1 订单中的分配率显示
- **CPU分配率**: 使用 `getCpuColor()` 函数
- **内存分配率**: 使用 `getMemColor()` 函数
- **Progress组件**: 根据分配率自动设置颜色

#### 2.2 策略阈值配置显示
- **触发阈值**: 使用 `getThresholdColor()` 函数
- **目标值**: 使用 `getTargetColor()` 函数
- **标签背景**: 动态设置背景颜色

## 实现细节

### 1. 新增颜色函数

#### 1.1 阈值颜色函数
```typescript
const getThresholdColor = (value: number): string => {
  if (value >= 80) return '#f5222d'; // 红色 - 高风险
  if (value >= 70) return '#faad14'; // 黄色 - 中风险
  if (value >= 55) return '#52c41a'; // 绿色 - 正常
  return '#1890ff'; // 蓝色 - 低使用率
};
```

#### 1.2 目标值颜色函数
```typescript
const getTargetColor = (value: number): string => {
  if (value >= 80) return '#faad14'; // 黄色 - 目标值较高
  if (value >= 55) return '#52c41a'; // 绿色 - 理想目标值
  return '#1890ff'; // 蓝色 - 保守目标值
};
```

### 2. 策略表格更新

#### 2.1 CPU阈值配置
**修改前**:
```tsx
<div style={{ background: '#ff4d4f', color: 'white', ... }}>
  {record.cpuThresholdValue}%
</div>
<div style={{ background: '#52c41a', color: 'white', ... }}>
  {record.cpuTargetValue}%
</div>
```

**修改后**:
```tsx
<div style={{ background: getThresholdColor(record.cpuThresholdValue), color: 'white', ... }}>
  {record.cpuThresholdValue}%
</div>
<div style={{ background: getTargetColor(record.cpuTargetValue), color: 'white', ... }}>
  {record.cpuTargetValue}%
</div>
```

#### 2.2 内存阈值配置
同样的逻辑应用到内存阈值和目标值的显示。

### 3. 颜色语义化

#### 3.1 触发阈值颜色语义
- **红色 (≥80%)**: 表示高风险阈值，需要立即干预
- **黄色 (70-79%)**: 表示警告阈值，需要关注
- **绿色 (55-69%)**: 表示正常阈值，合理范围
- **蓝色 (<55%)**: 表示保守阈值，资源充足

#### 3.2 目标值颜色语义
- **黄色 (≥80%)**: 目标值设置较高，可能仍有压力
- **绿色 (55-79%)**: 理想的目标值范围
- **蓝色 (<55%)**: 保守的目标值，资源利用率较低

## 视觉效果示例

### 1. 高负载入池策略
```
CPU  [85%] ↓ [70%]  (红色阈值 → 黄色目标)
MEM  [90%] ↓ [65%]  (红色阈值 → 绿色目标)
```

### 2. 中等负载策略
```
CPU  [75%] ↓ [60%]  (黄色阈值 → 绿色目标)
MEM  [70%] ↓ [55%]  (黄色阈值 → 绿色目标)
```

### 3. 低负载退池策略
```
CPU  [50%] ↑ [65%]  (蓝色阈值 → 绿色目标)
MEM  [45%] ↑ [60%]  (蓝色阈值 → 绿色目标)
```

## 优势分析

### 1. 视觉一致性
- **统一标准**: 所有百分比显示使用相同的颜色分级
- **认知负担**: 用户只需学习一套颜色语义
- **品牌一致**: 整个系统的颜色使用保持统一

### 2. 语义化表达
- **直观理解**: 颜色直接反映数值的风险等级
- **快速识别**: 用户可以快速识别高风险配置
- **决策支持**: 颜色帮助用户做出更好的配置决策

### 3. 用户体验
- **降低学习成本**: 统一的颜色规则减少用户学习负担
- **提高效率**: 快速的视觉识别提高操作效率
- **减少错误**: 清晰的视觉提示减少配置错误

## 测试要点

### 1. 颜色正确性测试
- [ ] 80%以上显示红色
- [ ] 70-79%显示黄色
- [ ] 55-69%显示绿色
- [ ] 55%以下显示蓝色

### 2. 一致性测试
- [ ] 策略表格中的阈值颜色正确
- [ ] 订单卡片中的分配率颜色正确
- [ ] 两处颜色规则完全一致

### 3. 边界值测试
- [ ] 80%边界值显示正确
- [ ] 70%边界值显示正确
- [ ] 55%边界值显示正确

### 4. 视觉效果测试
- [ ] 颜色对比度足够，文字清晰可读
- [ ] 不同颜色之间区分度明显
- [ ] 在不同屏幕和浏览器下显示一致

## 后续优化建议

### 1. 可配置化
- 考虑将颜色阈值设置为可配置参数
- 支持不同业务场景的自定义颜色规则

### 2. 无障碍支持
- 为色盲用户提供额外的视觉提示（如图标、形状）
- 确保颜色符合WCAG无障碍标准

### 3. 主题支持
- 支持深色主题下的颜色适配
- 确保在不同主题下颜色语义保持一致

### 4. 动态反馈
- 考虑添加颜色渐变动画
- 在数值变化时提供平滑的颜色过渡效果
