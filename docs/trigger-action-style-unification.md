# 触发动作样式统一

## 修改概述

统一新建监控策略和编辑监控策略模态框中的触发动作下拉框样式，使其与创建订单模态框中的动作类型样式保持一致。

## 修改内容

### 1. 问题描述

**修改前的样式差异**：
- **创建订单模态框**: 动作类型选项带有图标（入池/退池图标）
- **新建监控策略**: 触发动作选项为纯文本（无图标）
- **编辑监控策略**: 触发动作选项为纯文本（无图标）

### 2. 统一后的样式

#### 2.1 新建监控策略模态框

**文件**: `web/navy-fe/src/components/ElasticScaling/Dashboard.tsx`
**行号**: 2033-2046

**修改前**:
```tsx
<Select placeholder="请选择动作">
  <Option value="pool_entry">入池</Option>
  <Option value="pool_exit">退池</Option>
</Select>
```

**修改后**:
```tsx
<Select placeholder="请选择动作" showArrow style={{ width: '100%' }}>
  <Option value="pool_entry">
    <CloudUploadOutlined style={{ color: '#1890ff', marginRight: 4 }} /> 入池
  </Option>
  <Option value="pool_exit">
    <CloudDownloadOutlined style={{ color: '#ff7a45', marginRight: 4 }} /> 退池
  </Option>
</Select>
```

#### 2.2 编辑监控策略模态框

**文件**: `web/navy-fe/src/components/ElasticScaling/Dashboard.tsx`
**行号**: 2442-2455

**修改前**:
```tsx
<Select placeholder="请选择动作">
  <Option value="pool_entry">入池</Option>
  <Option value="pool_exit">退池</Option>
</Select>
```

**修改后**:
```tsx
<Select placeholder="请选择动作" showArrow style={{ width: '100%' }}>
  <Option value="pool_entry">
    <CloudUploadOutlined style={{ color: '#1890ff', marginRight: 4 }} /> 入池
  </Option>
  <Option value="pool_exit">
    <CloudDownloadOutlined style={{ color: '#ff7a45', marginRight: 4 }} /> 退池
  </Option>
</Select>
```

### 3. 样式统一特点

#### 3.1 一致的视觉元素
- **入池图标**: `CloudUploadOutlined` (蓝色 #1890ff)
- **退池图标**: `CloudDownloadOutlined` (橙色 #ff7a45)
- **图标间距**: 统一为 4px (`marginRight: 4`)
- **下拉箭头**: 所有选择器都显示箭头 (`showArrow`)
- **宽度设置**: 明确设置 `width: '100%'`

#### 3.2 颜色语义化
- **蓝色 (#1890ff)**: 表示入池操作，与系统主色调一致
- **橙色 (#ff7a45)**: 表示退池操作，与警告/移除操作色调一致

### 4. 三个模态框的动作选择器对比

#### 4.1 创建订单模态框
- **字段名**: `actionType` (动作类型)
- **选项**: 入池/退池 + 图标
- **功能**: 单选

#### 4.2 新建监控策略模态框
- **字段名**: `thresholdTriggerAction` (触发动作)
- **选项**: 入池/退池 + 图标 ✅
- **功能**: 单选

#### 4.3 编辑监控策略模态框
- **字段名**: `thresholdTriggerAction` (触发动作)
- **选项**: 入池/退池 + 图标 ✅
- **功能**: 单选

### 5. 图标导入确认

所需图标已在文件顶部正确导入：
```tsx
import {
  // ... 其他图标
  CloudUploadOutlined, CloudDownloadOutlined,
  // ... 其他图标
} from '@ant-design/icons';
```

## 测试要点

### 1. 视觉一致性测试
- [ ] 三个模态框的动作选择器样式完全一致
- [ ] 入池选项显示蓝色上传图标
- [ ] 退池选项显示橙色下载图标
- [ ] 图标与文本间距合适

### 2. 功能测试
- [ ] 新建策略模态框的触发动作选择正常
- [ ] 编辑策略模态框的触发动作选择正常
- [ ] 创建订单模态框的动作类型选择正常
- [ ] 所有选择器的下拉功能正常

### 3. 交互测试
- [ ] 下拉箭头正常显示和点击
- [ ] 选项悬停效果正常
- [ ] 选择后的显示效果正常

### 4. 响应式测试
- [ ] 不同屏幕尺寸下显示正常
- [ ] 图标和文本不会重叠或错位

## 优势

1. **视觉一致性**: 所有模态框的动作选择器样式统一，提供一致的用户体验
2. **语义化设计**: 图标颜色与操作类型语义匹配，提高可识别性
3. **用户友好**: 图标化的选项降低用户认知负担
4. **品牌一致性**: 使用统一的设计语言和色彩规范

## 注意事项

1. **功能完整性**: 修改仅涉及样式，不影响原有业务逻辑
2. **兼容性**: 保持与现有表单验证和数据处理的兼容
3. **可维护性**: 统一的样式便于后续维护和更新
4. **性能**: 图标组件不会影响页面性能

## 后续建议

1. **扩展应用**: 可以考虑在其他相关的动作选择器中应用相同的设计模式
2. **主题支持**: 如果系统支持主题切换，确保图标颜色能够适配不同主题
3. **国际化**: 如果需要支持多语言，确保图标的语义在不同文化中都能被理解
