# TypeScript Align 类型错误修复

## 错误描述

在策略表格列配置中，`align` 属性的类型不匹配导致TypeScript编译错误：

```
ERROR in src/components/ElasticScaling/Dashboard.tsx:1660:15
TS2322: Type 'string' is not assignable to type 'AlignType | undefined'.
```

## 错误原因

Ant Design Table组件的`align`属性期望的是`AlignType`类型，而不是普通的字符串类型。

### 修改前
```typescript
{
  title: '操作',
  key: 'action',
  width: 150,
  align: 'center',  // ❌ TypeScript推断为string类型
  render: (text: string, record: Strategy) => (
    // ...
  )
}
```

### 修改后
```typescript
{
  title: '操作',
  key: 'action',
  width: 150,
  align: 'center' as const,  // ✅ 明确指定为字面量类型
  render: (text: string, record: Strategy) => (
    // ...
  )
}
```

## 解决方案

使用TypeScript的`as const`断言，将字符串字面量`'center'`明确指定为常量类型，这样TypeScript就能正确推断出它是`AlignType`的有效值。

## AlignType 可选值

Ant Design Table组件的`align`属性支持以下值：
- `'left'`
- `'center'`
- `'right'`

## 类似问题的预防

在使用Ant Design组件时，对于有特定类型要求的属性，建议：

1. **使用as const断言**：
   ```typescript
   align: 'center' as const
   ```

2. **直接使用枚举或常量**：
   ```typescript
   import { AlignType } from 'antd/es/table/interface';
   align: 'center' as AlignType
   ```

3. **查阅组件文档**：
   确认属性的正确类型定义

## 测试验证

- [x] TypeScript编译无错误
- [x] 表格操作列居中对齐显示正常
- [x] 功能无影响

## 总结

这是一个典型的TypeScript类型推断问题。通过使用`as const`断言，我们告诉TypeScript将字符串字面量视为常量类型而不是通用的string类型，从而满足了Ant Design组件的类型要求。

这种修复方式既保持了代码的简洁性，又确保了类型安全。
