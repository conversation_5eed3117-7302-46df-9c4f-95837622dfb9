-- 添加订单名称和描述字段到弹性伸缩订单表
-- Migration: add_order_name_description
-- Date: 2024-12-01

-- 添加订单名称字段
ALTER TABLE elastic_scaling_order 
ADD COLUMN name VARCHAR(255) DEFAULT '' COMMENT '订单名称';

-- 添加订单描述字段
ALTER TABLE elastic_scaling_order 
ADD COLUMN description TEXT COMMENT '订单描述';

-- 为现有记录设置默认值
-- 策略触发的订单使用策略名称作为订单名称
UPDATE elastic_scaling_order eso
LEFT JOIN elastic_scaling_strategy ess ON eso.strategy_id = ess.id
SET eso.name = CASE 
    WHEN ess.name IS NOT NULL THEN ess.name
    ELSE CONCAT('手动订单-', eso.order_number)
END
WHERE eso.name = '' OR eso.name IS NULL;

-- 为现有记录设置描述
UPDATE elastic_scaling_order eso
LEFT JOIN elastic_scaling_strategy ess ON eso.strategy_id = ess.id
SET eso.description = CASE 
    WHEN ess.description IS NOT NULL THEN CONCAT('策略触发: ', ess.description)
    ELSE CONCAT('手动创建的', CASE WHEN eso.action_type = 'pool_entry' THEN '入池' ELSE '退池' END, '订单')
END
WHERE eso.description = '' OR eso.description IS NULL;

-- 添加索引以提高查询性能
CREATE INDEX idx_elastic_scaling_order_name ON elastic_scaling_order(name);

-- 验证数据
SELECT 
    id,
    order_number,
    name,
    description,
    action_type,
    strategy_id,
    created_at
FROM elastic_scaling_order 
ORDER BY created_at DESC 
LIMIT 10;
