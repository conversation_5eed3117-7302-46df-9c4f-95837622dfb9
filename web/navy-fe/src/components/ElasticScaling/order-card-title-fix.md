# 手动创建订单卡片标题显示修复

## 问题描述

手动创建的订单在卡片表头显示为"手动创建 - 入池/退池"，用户希望显示订单名称而不是固定的"手动创建"文本。

## 解决方案

### 方案选择

考虑到以下因素：
1. 后端 `ElasticScalingOrder` 模型没有 `name` 字段
2. 前端 `OrderListItem` 类型定义也没有 `name` 字段
3. 避免大规模的后端数据库结构修改

选择使用现有的 `orderNumber` 字段来显示订单标识。

### 修改内容

**文件**: `web/navy-fe/src/components/ElasticScaling/Dashboard.tsx`
**行号**: 834
**修改前**:
```typescript
{order.strategyName ? `${order.strategyName}` : (order.name || '手动创建')} - {order.actionType === 'pool_entry' ? '入池' : '退池'}
```

**修改后**:
```typescript
{order.strategyName ? `${order.strategyName}` : order.orderNumber} - {order.actionType === 'pool_entry' ? '入池' : '退池'}
```

### 显示逻辑

1. **策略触发的订单**: 显示策略名称
   - 格式: `{策略名称} - {入池/退池}`
   - 示例: `高负载自动扩容策略 - 入池`

2. **手动创建的订单**: 显示订单号
   - 格式: `{订单号} - {入池/退池}`
   - 示例: `ES-20241201-001 - 入池`

### 优势

1. **唯一性**: 订单号是唯一的，能够准确标识每个订单
2. **一致性**: 所有订单都有订单号，不会出现空值
3. **可追溯性**: 订单号通常包含时间信息，便于追溯
4. **无需后端修改**: 利用现有字段，避免数据库结构变更

### 测试要点

1. **策略订单**: 验证策略触发的订单仍显示策略名称
2. **手动订单**: 验证手动创建的订单显示订单号
3. **订单号格式**: 确认订单号格式符合预期
4. **界面一致性**: 确保所有订单卡片显示一致

### 后续优化建议

如果需要更友好的显示名称，可以考虑：

1. **添加 name 字段**: 在后端模型中添加 `name` 字段
2. **前端输入**: 在创建订单时允许用户输入自定义名称
3. **默认命名**: 为手动订单生成更友好的默认名称

但当前方案已经满足了基本需求，显示订单号比固定的"手动创建"文本更有意义。
